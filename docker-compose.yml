version: "3.7"

x-defaults: &defaults
  restart: unless-stopped
  networks:
    - open-webui
  extra_hosts:
    - host.docker.internal:host-gateway
x-environment: &proxy_environment
  http_proxy: ${proxy_url}
  https_proxy: ${proxy_url}
  no_proxy: localhost,127.0.0.1,host.docker.internal,api-proxy.me,.oaipro.com,server0281.xyz,deepseek.com,siliconflow.cn,qwen2api,dangbei2api,tts,new-api,redis,postgres,pipelines,open-webui,openwebui-monitor,mtranserver,sillytavern
x-watchtower: &watchtower
  labels:
    - com.centurylinklabs.watchtower.enable=true
x-logging: &default-logging
  logging:
    driver: "json-file"
    options:
      max-size: "10m"
      max-file: "3"


services:
  tts:
    image: mzzsfy/tts:latest
    container_name: tts
    <<: [*defaults, *watchtower, *default-logging]
    environment:
      TTS_STRICT_MODE: true

  qwen2api:
    image: rfym21/qwen2api:latest
    container_name: qwen2api
    <<: [*defaults, *watchtower, *default-logging]
    ports:
      - "${qwen2api_port}:3000"
    environment:
      # API 路径，不填则为空(http://localhost:3000)
      # 示例(/api) 则访问 http://localhost:3000/api
      API_PREFIX: /api
      # 如果需要修改Docker暴露端口，请修改ports中的参数
      # 示例(8080:3000) 则访问 http://localhost:8080
      SERVICE_PORT: 3000
      # API 密钥 (非必填)
      # 如果需要使用多账户或使用内置账户，请填写
      API_KEY: ${qwen2api_key}
      # 监听地址(非必填)
      LISTEN_ADDRESS:
      # 搜索信息展示模式
      # table: 使用折叠块和表格展示
      # text: 使用纯文本
      SEARCH_INFO_MODE: table
      # 是否输出思考过程
      OUTPUT_THINK: true
      REDIS_URL: ${qwen2api_redis_url}
      DATA_SAVE_MODE: redis

  dangbei2api:
    #build:
    #  context: ./dangbei2api/
    #  dockerfile: Dockerfile
    #image: dangbei/api-proxy:latest  # 添加了镜像名称配置
    image: xy2yp/dangbei2api:latest
    container_name: dangbei2api
    <<: [*defaults, *watchtower, *default-logging]
    ports:
      - "${dangbei2api_port}:8000"
    environment:
      API_KEY: ${dangbei2api_key}
      API_DOMAIN: ${dangbei2api_domain}
    #healthcheck:
    #  test: ["CMD", "curl", "-f", "http://localhost:8000"]
    #  interval: 30s
    #  timeout: 10s
    #  retries: 3

  pplx2api:
    image: ghcr.io/yushangxiao/pplx2api:latest
    container_name: pplx2api
    <<: [*defaults, *watchtower, *default-logging]
    ports:
      - "${pplx2api_port}:8085"
    environment:
      <<: *proxy_environment
      SESSIONS: ${pplx2api_sessions}
      ADDRESS: 0.0.0.0:8085
      APIKEY: ${pplx2api_key}
      PROXY: ${proxy_url}  # 可选
      MAX_CHAT_HISTORY_LENGTH: 10000
      NO_ROLE_PREFIX: false
      IS_INCOGNITO: true
      SEARCH_RESULT_COMPATIBLE: false

  redis:
    image: redis:7-alpine
    container_name: redis
    <<: [*defaults, *default-logging]
    volumes:
      - ./redis/data:/data
      - ./redis/redis.conf:/usr/local/etc/redis/redis.conf
    command: redis-server /usr/local/etc/redis/redis.conf
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  postgres:
    container_name: postgres
    image: postgres:17-alpine
    <<: [*defaults, *default-logging]
    environment:
      POSTGRES_USER: ${postgres_user}
      POSTGRES_PASSWORD: ${postgres_password}
      POSTGRES_DB: ${postgres_db}
      POSTGRES_MULTIPLE_DATABASES: ${postgres_multiple_databases}
    volumes:
      - ./postgres_data:/var/lib/postgresql/data
      - ./postgres-init:/docker-entrypoint-initdb.d
    ports:
      - "${postgres_port}:5432"
    #user: postgres
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U openwebui | grep -o 'accepting connections'"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s

  new-api:
    image: calciumion/new-api:latest
    container_name: new-api
    <<: [*defaults, *watchtower, *default-logging]
    command: --log-dir /app/logs
    ports:
      - "${newapi_port}:3000"
    environment:
      #<<: *proxy_environment
      SQL_DSN: ${newapi_db_url}
      REDIS_CONN_STRING: ${newapi_db_redis_url}
      SESSION_SECRET: ${newapi_session_secret}
      TZ: Asia/Shanghai
    volumes:
      - ./new-api/data:/data
      - ./new-api/logs:/app/logs
    depends_on:
      redis:
        condition: service_healthy
      tts:
        condition: service_started
      dangbei2api:
        condition: service_started
      qwen2api:
        condition: service_started
      pplx2api:
        condition: service_started
    healthcheck:
      test: ["CMD-SHELL", "wget -q -O - http://localhost:${newapi_port}/api/status | grep -o '\"success\":\\s*true' | awk -F: '{print $2}'"]
      interval: 10s
      timeout: 5s
      retries: 10
      start_period: 5s

#  pipelines:
#    image: ghcr.io/open-webui/pipelines:main
#    container_name: pipelines
#    <<: [*defaults, *watchtower, *default-logging]
#    # ports:
#    #   - "9099:9099"
#    environment: *proxy_environment
#    volumes:
#      - ./pipelines:/app/pipelines
#    healthcheck:
#      test: ["CMD-SHELL", "curl -f http://localhost:9099/ | grep -o '\"status\":\\s*true' | awk -F: '{print $2}'"]
#      interval: 30s
#      timeout: 10s
#      retries: 3
#      start_period: 20s

  open-webui:
    container_name: open-webui
    image: ghcr.io/open-webui/open-webui:main
    <<: [*defaults, *default-logging]
    ports:
      - "${openwebui_port}:8080"
    volumes:
      - ./open-webui_data:/app/backend/data
      - ./open-webui-assets-custom:/app/build/assets/custom
      - ./open-webui-init.sh:/app/backend/open-webui-init.sh
    environment:
      #<<: *proxy_environment
      OLLAMA_BASE_URL: false
      SEARXNG_URL: false
      ENABLE_REALTIME_CHAT_SAVE: false
      DATABASE_URL: ${openwebui_database_url}
      DATABASE_POOL_SIZE: 16
      DATABASE_POOL_MAX_OVERFLOW: 20
      REDIS_URL: ${openwebui_redis_url}
      ENABLE_WEBSOCKET_SUPPORT: True
      WEBSOCKET_MANAGER: redis
      ENABLE_OLLAMA_API: false
      AIOHTTP_CLIENT_TIMEOUT_OPENAI_MODEL_LIST: 5
      #AIOHTTP_CLIENT_TIMEOUT: 10
      HF_ENDPOINT: https://hf-mirror.com
      WEBUI_SECRET_KEY: ${openwebui_secret_key}
      HOST: "0.0.0.0"
    depends_on:
      postgres:
        condition: service_healthy
      new-api:
        condition: service_healthy
 #     pipelines:
 #       condition: service_healthy
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:8080/health | grep -o '\"status\":\\s*true' | awk -F: '{print $2}'"]
      interval: 10s
      timeout: 5s
      retries: 10
      start_period: 5s
    command:
      - /bin/bash
      - -c
      - |
        bash /app/backend/open-webui-init.sh

  openwebui-monitor:
    container_name: openwebui-monitor
    image: docker.io/variantconst/openwebui-monitor:latest
    <<: [*defaults, *watchtower, *default-logging]
    ports:
      - "${openwebui_monitor_port}:3000"
    environment:
      POSTGRES_HOST: ${openwebui_monitor_postgres_host}
      POSTGRES_PORT: ${openwebui_monitor_postgres_port}
      POSTGRES_USER: ${openwebui_monitor_postgres_user}
      POSTGRES_PASSWORD: ${openwebui_monitor_postgres_password}
      POSTGRES_DATABASE: ${openwebui_monitor_postgres_database}
    depends_on:
      postgres:
        condition: service_healthy

  mtranserver:
    image: xxnuo/mtranserver:latest
    container_name: mtranserver
    <<: [*defaults,  *watchtower, *default-logging]
    ports:
      - "${mtranserver_port}:8989"
    volumes:
      - ./mtranserver/models:/app/models
    environment:
      CORE_API_TOKEN: sk_G6a5TjaKL85zQs

  sillytavern:
    container_name: sillytavern
    <<: [*defaults, *default-logging]
    image: ghcr.io/sillytavern/sillytavern:latest
    environment:
      NODE_ENV: production
      FORCE_COLOR: 1
    ports:
      - "${sillytavern_port}:8010"
    volumes:
      - "./sillytavern/config:/home/<USER>/app/config"
      - "./sillytavern/data:/home/<USER>/app/data"
      - "./sillytavern/plugins:/home/<USER>/app/plugins"
      - "./sillytavern/extensions:/home/<USER>/app/public/scripts/extensions/third-party"

  mcpo:
    container_name: mcpo
    <<: [*defaults, *watchtower, *default-logging]
    image: ghcr.io/open-webui/mcpo:main
    ports:
      - "${mcpo_port}:8000"
    environment: *proxy_environment
    volumes:
      - ./mcpo/config.json:/app/config.json
      - ./mcpo/mcp:/home/<USER>
    command: --config config.json

networks:
  open-webui:
    driver: bridge
    enable_ipv6: true
    ipam:
      driver: default
      config:
        - subnet: fd00:db8:1234::/64
          gateway: fd00:db8:1234::1
